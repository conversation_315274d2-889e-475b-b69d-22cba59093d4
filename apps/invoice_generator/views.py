from django.shortcuts import render
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from rest_framework import status, generics
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.renderers import StaticHTMLRenderer
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.parsers import MultiPartParser, FormParser
import os
from django.conf import settings
from .models import CompanyTemplate, SalesWorkflow, SalesInvoiceData
from .serializers import (
    CompanyTemplateSerializer,
    CompanyTemplateListSerializer,
    SalesWorkflowSerializer,
    ColumnMappingRequestSerializer,
    ColumnMappingResponseSerializer,
    WorkflowStartRequestSerializer,
)
from .csv_processor import CSVProcessor
from utils.logger import get_logger

logger = get_logger("invoice_generator.views")

# Create your views here


@api_view(["POST"])
def extract_template_information(request):
    """
    Endpoint to extract information from an uploaded invoice PDF file.
    Returns dummy data for now.
    """
    if "file" not in request.FILES:
        return JsonResponse({"error": "No PDF file provided"}, status=400)

    pdf_file = request.FILES["file"]

    # Here we would process the PDF, but for now we return dummy data
    invoice_data = {
        "company_name": "Example Company Ltd",
        "address": "123 Business Street, City, Country",
        "contact_email": "<EMAIL>",
        "payment_terms": "Net 30 days",
        "bank_info": "Bank: Example Bank, Account: ********, Sort Code: 01-02-03",
        "company_logo": "base64_encoded_image_data_would_be_here",
        "Template Name": "",
    }

    return JsonResponse(invoice_data)


class InvoiceTemplateListView(APIView):
    """
    API endpoint to fetch all available invoice templates.
    Returns a list of templates with their metadata and raw HTML content.
    """

    permission_classes = [AllowAny]

    def get(self, request):
        templates_dir = os.path.join(settings.BASE_DIR, "invoice_templates")
        templates = []

        # Template metadata
        template_info = {
            "clean_business.html": {
                "name": "Clean Business",
                "description": "A clean and professional business invoice template",
                "preview_image": "https://placehold.co/300x400/059669/ffffff?text=Clean+Business",
            },
            "corporate.html": {
                "name": "Corporate",
                "description": "A formal corporate invoice template with traditional styling",
                "preview_image": "https://placehold.co/300x400/1f2937/ffffff?text=Corporate",
            },
            "minimalist.html": {
                "name": "Minimalist",
                "description": "A simple and elegant minimalist invoice template",
                "preview_image": "https://placehold.co/300x400/64748b/ffffff?text=Minimalist",
            },
            "elegant_classic.html": {
                "name": "Elegant Classic",
                "description": "An elegant classic invoice template with decorative elements",
                "preview_image": "https://placehold.co/300x400/d97706/ffffff?text=Elegant+Classic",
            },
            "contemporary.html": {
                "name": "Contemporary",
                "description": "A modern contemporary invoice template with vibrant colors",
                "preview_image": "https://placehold.co/300x400/7c3aed/ffffff?text=Contemporary",
            },
        }

        try:
            for filename in os.listdir(templates_dir):
                if filename.endswith(".html"):
                    file_path = os.path.join(templates_dir, filename)
                    with open(file_path, "r", encoding="utf-8") as file:
                        html_content = file.read()

                    template_data = {
                        "id": filename.replace(".html", ""),
                        "filename": filename,
                        "html_content": html_content,
                        **template_info.get(
                            filename,
                            {
                                "name": filename.replace(".html", "")
                                .replace("_", " ")
                                .title(),
                                "description": f"Invoice template: {filename}",
                                "preview_image": "/static/template-previews/default.png",
                            },
                        ),
                    }
                    templates.append(template_data)

            return Response(
                {"success": True, "templates": templates, "count": len(templates)}
            )

        except Exception as e:
            return Response({"success": False, "error": str(e)}, status=500)


class InvoiceTemplateDetailView(APIView):
    """
    API endpoint to fetch a specific invoice template by ID.
    Returns the raw HTML content using StaticHTMLRenderer.
    """

    renderer_classes = [StaticHTMLRenderer]
    permission_classes = [AllowAny]

    def get(self, request, template_id):
        templates_dir = os.path.join(settings.BASE_DIR, "invoice_templates")
        template_file = f"{template_id}.html"
        file_path = os.path.join(templates_dir, template_file)

        try:
            if not os.path.exists(file_path):
                return Response(
                    "<html><body><h1>Template not found</h1></body></html>", status=404
                )

            with open(file_path, "r", encoding="utf-8") as file:
                html_content = file.read()

            return Response(html_content)

        except Exception as e:
            return Response(
                f"<html><body><h1>Error loading template: {str(e)}</h1></body></html>",
                status=500,
            )


# Company Template Views


class CompanyTemplateListCreateView(generics.ListCreateAPIView):
    """
    API endpoint for listing and creating company templates.
    """

    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return CompanyTemplateListSerializer
        return CompanyTemplateSerializer

    def get_queryset(self):
        return CompanyTemplate.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)


class CompanyTemplateDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API endpoint for retrieving, updating, and deleting company templates.
    """

    serializer_class = CompanyTemplateSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return CompanyTemplate.objects.filter(user=self.request.user)

    def perform_update(self, serializer):
        # Update last_used timestamp when template is updated
        serializer.save(last_used=timezone.now())


# Sales Workflow Views


class SalesWorkflowListCreateView(generics.ListCreateAPIView):
    """
    API endpoint for listing and creating sales workflows.
    """

    serializer_class = SalesWorkflowSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return SalesWorkflow.objects.filter(user=self.request.user)


class SalesWorkflowDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API endpoint for retrieving, updating, and deleting sales workflows.
    """

    serializer_class = SalesWorkflowSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return SalesWorkflow.objects.filter(user=self.request.user)


class CSVUploadView(APIView):
    """
    API endpoint for uploading CSV files and detecting columns.
    """

    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request):
        try:
            if "file" not in request.FILES:
                return Response(
                    {"error": "No file provided"}, status=status.HTTP_400_BAD_REQUEST
                )

            file = request.FILES["file"]

            # Validate file type
            if not file.name.endswith((".csv", ".CSV")):
                return Response(
                    {"error": "Only CSV files are allowed"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Detect columns
            columns = CSVProcessor.detect_columns(file)

            # Generate suggested mappings
            mapping_result = CSVProcessor.suggest_column_mappings(columns)

            return Response(
                {
                    "success": True,
                    "detected_columns": columns,
                    "suggested_mappings": mapping_result["suggested_mappings"],
                    "confidence_scores": mapping_result["confidence_scores"],
                }
            )

        except Exception as e:
            logger.error(f"Error processing CSV upload: {str(e)}")
            return Response(
                {"error": f"Failed to process CSV file: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ColumnMappingSuggestionsView(APIView):
    """
    API endpoint for getting column mapping suggestions.
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = ColumnMappingRequestSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            csv_columns = serializer.validated_data["csv_columns"]
            mapping_result = CSVProcessor.suggest_column_mappings(csv_columns)

            response_serializer = ColumnMappingResponseSerializer(mapping_result)
            return Response(response_serializer.data)

        except Exception as e:
            logger.error(f"Error generating column mapping suggestions: {str(e)}")
            return Response(
                {"error": f"Failed to generate suggestions: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class StartWorkflowView(APIView):
    """
    API endpoint for starting a new sales workflow.
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            serializer = WorkflowStartRequestSerializer(
                data=request.data, context={"request": request}
            )
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            company_template_id = serializer.validated_data["company_template_id"]
            company_template = CompanyTemplate.objects.get(
                id=company_template_id, user=request.user
            )

            # Create new workflow
            workflow = SalesWorkflow.objects.create(
                user=request.user,
                company_template=company_template,
                status="file_upload",
            )

            # Update template last_used timestamp
            company_template.last_used = timezone.now()
            company_template.save()

            workflow_serializer = SalesWorkflowSerializer(workflow)
            return Response({"success": True, "workflow": workflow_serializer.data})

        except CompanyTemplate.DoesNotExist:
            return Response(
                {"error": "Company template not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            logger.error(f"Error starting workflow: {str(e)}")
            return Response(
                {"error": f"Failed to start workflow: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
