import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { toast } from "react-hot-toast";

const SalesColumnMapping = ({ 
  onNext, 
  onBack, 
  variants, 
  csvData, 
  companyTemplate 
}) => {
  const [mappings, setMappings] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  // Invoice field definitions
  const invoiceFields = [
    { id: 'invoice_number', label: 'Invoice Number', required: true, description: 'Unique identifier for the invoice' },
    { id: 'bill_date', label: 'Bill Date', required: false, description: 'Date when the invoice was issued' },
    { id: 'due_date', label: 'Due Date', required: false, description: 'Payment due date' },
    { id: 'client_company', label: 'Client Company', required: true, description: 'Name of the client company' },
    { id: 'contact_email', label: 'Contact Email', required: false, description: 'Client contact email address' },
    { id: 'client_address', label: 'Client Address', required: false, description: 'Client billing address' },
    { id: 'phone_number', label: 'Phone Number', required: false, description: 'Client phone number' },
    { id: 'service_description', label: 'Service Description', required: false, description: 'Description of services/products' },
    { id: 'quantity', label: 'Quantity', required: false, description: 'Quantity of items/services' },
    { id: 'unit_rate', label: 'Unit Rate', required: false, description: 'Price per unit' },
    { id: 'line_amount', label: 'Line Amount', required: true, description: 'Total amount for this line item' },
    { id: 'tax_percent', label: 'Tax Percent', required: false, description: 'Tax rate percentage' },
    { id: 'tax_value', label: 'Tax Value', required: false, description: 'Tax amount' },
    { id: 'discount_amount', label: 'Discount Amount', required: false, description: 'Discount applied' },
    { id: 'order_reference', label: 'Order Reference', required: false, description: 'Purchase order or reference number' },
    { id: 'notes', label: 'Notes', required: false, description: 'Additional notes or comments' }
  ];

  // Initialize mappings with suggested mappings
  useEffect(() => {
    if (csvData?.suggestedMappings) {
      setMappings(csvData.suggestedMappings);
    }
  }, [csvData]);

  const handleMappingChange = (fieldId, csvColumn) => {
    setMappings(prev => ({
      ...prev,
      [fieldId]: csvColumn
    }));
  };

  const validateMappings = () => {
    const requiredFields = invoiceFields.filter(field => field.required);
    const missingFields = requiredFields.filter(field => !mappings[field.id]);
    
    if (missingFields.length > 0) {
      const missingFieldNames = missingFields.map(field => field.label).join(', ');
      setError(`Please map the following required fields: ${missingFieldNames}`);
      return false;
    }
    
    setError("");
    return true;
  };

  const handleSubmit = async () => {
    if (!validateMappings()) {
      return;
    }

    setLoading(true);
    
    try {
      // Pass the mappings to the next step
      onNext({
        ...csvData,
        columnMappings: mappings,
        companyTemplate: companyTemplate
      });
      
      toast.success("Column mappings saved successfully!");
    } catch (err) {
      console.error("Error saving mappings:", err);
      setError("Failed to save column mappings. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const getConfidenceColor = (fieldId) => {
    const confidence = csvData?.confidenceScores?.[fieldId] || 0;
    if (confidence >= 0.9) return 'text-green-600';
    if (confidence >= 0.7) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getConfidenceIcon = (fieldId) => {
    const confidence = csvData?.confidenceScores?.[fieldId] || 0;
    if (confidence >= 0.9) {
      return (
        <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
    }
    if (confidence >= 0.7) {
      return (
        <svg className="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      );
    }
    return (
      <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    );
  };

  return (
    <motion.div 
      className="flex flex-col gap-6"
      variants={variants}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-medium text-gray-700">
            Map CSV Columns
          </h2>
          <p className="text-gray-600 mt-1">
            Map your CSV columns to invoice fields. We've suggested mappings based on column names.
          </p>
        </div>
        <button
          onClick={onBack}
          className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          ← Back
        </button>
      </div>

      {/* File Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center gap-3">
          <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <div>
            <p className="font-medium text-blue-900">
              {csvData?.file?.name} • {csvData?.detectedColumns?.length} columns detected
            </p>
            <p className="text-sm text-blue-700">
              Company: {companyTemplate?.company_name}
            </p>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <div className="flex items-start gap-2">
            <svg className="w-4 h-4 text-red-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-sm text-red-800">{error}</p>
          </div>
        </div>
      )}

      {/* Mapping Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Required Fields */}
        <div>
          <h3 className="text-lg font-medium text-gray-700 mb-4">Required Fields</h3>
          <div className="space-y-4">
            {invoiceFields.filter(field => field.required).map(field => (
              <div key={field.id} className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <label className="font-medium text-gray-900">
                    {field.label} <span className="text-red-500">*</span>
                  </label>
                  {mappings[field.id] && getConfidenceIcon(field.id)}
                </div>
                <p className="text-xs text-gray-600 mb-3">{field.description}</p>
                <select
                  value={mappings[field.id] || ""}
                  onChange={(e) => handleMappingChange(field.id, e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select a column...</option>
                  {csvData?.detectedColumns?.map((column, index) => (
                    <option key={index} value={column}>{column}</option>
                  ))}
                </select>
                {mappings[field.id] && csvData?.confidenceScores?.[field.id] && (
                  <p className={`text-xs mt-1 ${getConfidenceColor(field.id)}`}>
                    Confidence: {Math.round(csvData.confidenceScores[field.id] * 100)}%
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Optional Fields */}
        <div>
          <h3 className="text-lg font-medium text-gray-700 mb-4">Optional Fields</h3>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {invoiceFields.filter(field => !field.required).map(field => (
              <div key={field.id} className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <label className="font-medium text-gray-900">{field.label}</label>
                  {mappings[field.id] && getConfidenceIcon(field.id)}
                </div>
                <p className="text-xs text-gray-600 mb-3">{field.description}</p>
                <select
                  value={mappings[field.id] || ""}
                  onChange={(e) => handleMappingChange(field.id, e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select a column...</option>
                  {csvData?.detectedColumns?.map((column, index) => (
                    <option key={index} value={column}>{column}</option>
                  ))}
                </select>
                {mappings[field.id] && csvData?.confidenceScores?.[field.id] && (
                  <p className={`text-xs mt-1 ${getConfidenceColor(field.id)}`}>
                    Confidence: {Math.round(csvData.confidenceScores[field.id] * 100)}%
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <button
          onClick={handleSubmit}
          disabled={loading}
          className={`px-6 py-2 font-medium rounded-lg transition-colors ${
            loading
              ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {loading ? (
            <div className="flex items-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </div>
          ) : (
            "Continue to Invoice Generation"
          )}
        </button>
      </div>
    </motion.div>
  );
};

export default SalesColumnMapping;
